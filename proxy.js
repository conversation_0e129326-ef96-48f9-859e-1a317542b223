const axios = require('axios');

class ProxyHandler {
    constructor() {
        this.proxyUrl = 'https://getip.我使用的是花生的代理，可以自己让gpt适配其他的http代理文件删了也能跑.com/servers.php?session=--4d927ef1177e15a911a7dda24408d11c&time=3&count=1&type=text&only=1&pw=no&protocol=http&separator=1&iptype=tunnel&format=null&dev=web';
        this.currentProxy = null;
        this.proxyExpireTime = null;
    }

    // 获取新的代理
    async getProxy() {
        try {
            // 检查当前代理是否还有效（3分钟有效期）
            if (this.currentProxy && this.proxyExpireTime && Date.now() < this.proxyExpireTime) {
                console.log(`[PROXY] 使用缓存代理: ${this.currentProxy}`);
                return this.currentProxy;
            }

            console.log('[PROXY] 获取新代理...');
            
            const response = await axios.get(this.proxyUrl, {
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            if (response.data && typeof response.data === 'string') {
                const proxyData = response.data.trim();
                
                // 验证代理格式 (IP:PORT)
                if (this.isValidProxy(proxyData)) {
                    this.currentProxy = proxyData;
                    // 设置3分钟后过期
                    this.proxyExpireTime = Date.now() + (3 * 60 * 1000);
                    console.log(`[PROXY] ✅ 获取代理成功: ${this.currentProxy}`);
                    return this.currentProxy;
                } else {
                    console.log(`[PROXY] ❌ 代理格式无效: ${proxyData}`);
                    return null;
                }
            } else {
                console.log('[PROXY] ❌ 代理响应格式错误');
                return null;
            }

        } catch (error) {
            console.log(`[PROXY] ❌ 获取代理失败: ${error.message}`);
            return null;
        }
    }

    // 验证代理格式
    isValidProxy(proxy) {
        // 检查格式是否为 IP:PORT
        const proxyRegex = /^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$/;
        return proxyRegex.test(proxy);
    }

    // 测试代理连通性
    async testProxy(proxy) {
        try {
            console.log(`[PROXY] 测试代理连通性: ${proxy}`);
            
            const response = await axios.get('http://httpbin.org/ip', {
                proxy: {
                    host: proxy.split(':')[0],
                    port: parseInt(proxy.split(':')[1]),
                    protocol: 'http'
                },
                timeout: 10000
            });

            if (response.status === 200) {
                console.log(`[PROXY] ✅ 代理测试成功: ${proxy}`);
                console.log(`[PROXY] 代理IP: ${response.data.origin}`);
                return true;
            } else {
                console.log(`[PROXY] ❌ 代理测试失败: ${proxy}`);
                return false;
            }

        } catch (error) {
            console.log(`[PROXY] ❌ 代理测试失败: ${proxy} - ${error.message}`);
            return false;
        }
    }

    // 获取并测试代理
    async getValidProxy() {
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
            attempts++;
            
            const proxy = await this.getProxy();
            if (!proxy) {
                console.log(`[PROXY] 获取代理失败，尝试 ${attempts}/${maxAttempts}`);
                await this.wait(2000);
                continue;
            }

            // 测试代理连通性
            const isValid = await this.testProxy(proxy);
            if (isValid) {
                return proxy;
            } else {
                console.log(`[PROXY] 代理无效，重新获取... ${attempts}/${maxAttempts}`);
                this.currentProxy = null;
                this.proxyExpireTime = null;
                await this.wait(2000);
            }
        }

        console.log('[PROXY] ❌ 无法获取有效代理，将不使用代理');
        return null;
    }

    // 清除当前代理缓存
    clearProxy() {
        this.currentProxy = null;
        this.proxyExpireTime = null;
        console.log('[PROXY] 代理缓存已清除');
    }

    // 获取代理状态信息
    getProxyStatus() {
        if (!this.currentProxy) {
            return { hasProxy: false, proxy: null, timeLeft: 0 };
        }

        const timeLeft = this.proxyExpireTime ? Math.max(0, this.proxyExpireTime - Date.now()) : 0;
        
        return {
            hasProxy: true,
            proxy: this.currentProxy,
            timeLeft: Math.floor(timeLeft / 1000), // 秒
            expired: timeLeft <= 0
        };
    }

    // 辅助方法
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ProxyHandler;

