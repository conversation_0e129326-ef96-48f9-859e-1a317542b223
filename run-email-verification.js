const AutoRegister = require('./index.js');
const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');
require('dotenv').config();

async function runEmailVerification() {
    const autoRegister = new AutoRegister();
    const augmentAuth = new AugmentAuth();
    const tokenStorage = new TokenStorage();

    try {
        console.log('🚀 开始 Augment 授权和邮箱验证流程');
        console.log('');

        // 步骤1: 生成 Augment 授权 URL
        console.log('🔐 步骤1: 生成 Augment 授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();

        // 将生成的 URL 设置为环境变量，替换硬编码的 LINK_TO_TEST
        process.env.LINK_TO_TEST = authUrl;

        console.log('✅ 授权 URL 已生成并设置');
        console.log(`📧 目标URL: ${authUrl}`);
        console.log('📝 使用One Mail API生成临时邮箱');
        console.log('⏰ 将自动获取验证码（2分钟超时，每5秒检查一次）');
        console.log('📸 每一步都会自动截图和保存HTML');
        console.log('');

        // 步骤2: 执行邮箱验证流程（会使用我们生成的授权URL）
        console.log('🔐 步骤2: 执行邮箱验证和授权流程');
        const clipboardContent = await autoRegister.handleEmailVerificationWithOneMailAPI(authUrl);

        // 步骤3: 处理授权码并获取访问令牌
        if (clipboardContent) {
            console.log('');
            console.log('🔐 步骤3: 处理授权码并获取访问令牌');

            try {
                // 完成 OAuth 流程
                const tokenResponse = await augmentAuth.completeOAuthFlow(clipboardContent);

                // 保存令牌到 JSON 文件
                const tokenId = tokenStorage.addToken(tokenResponse, {
                    description: 'Auto-generated via email verification',
                    user_agent: 'augment-auto-email-verification',
                    session_id: `session_${Date.now()}`
                });

                console.log('');
                console.log('🎉 完整流程成功完成！');
                console.log(`💾 令牌已保存，ID: ${tokenId}`);
                console.log('📊 令牌统计:');
                tokenStorage.getStats();

            } catch (tokenError) {
                console.error('');
                console.error('❌ 令牌处理失败:', tokenError.message);
                console.error('📋 剪贴板内容已保存，可手动处理');
            }
        }

        console.log('');
        console.log('🎉 邮箱验证流程完成！');
        console.log('📁 请查看 image/ 目录中的截图和HTML文件');
        console.log('📁 请查看 tokens.json 文件中的令牌数据');

    } catch (error) {
        console.error('');
        console.error('💥 邮箱验证流程失败:', error.message);
        console.error('📁 请查看 image/ 目录中的错误截图和HTML文件进行调试');
        process.exit(1);
    }
}

if (require.main === module) {
    runEmailVerification();
}
