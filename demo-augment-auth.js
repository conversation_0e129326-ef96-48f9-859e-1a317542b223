const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');

/**
 * 演示 Augment 授权流程
 * 这个脚本展示了如何使用新的授权功能
 */
async function demoAugmentAuth() {
    console.log('🎬 Augment 授权流程演示');
    console.log('='.repeat(50));
    console.log('');

    try {
        // 步骤1: 创建授权实例
        console.log('📝 步骤1: 创建授权实例');
        const augmentAuth = new AugmentAuth();
        const tokenStorage = new TokenStorage();
        console.log('✅ 实例创建完成');
        console.log('');

        // 步骤2: 生成授权 URL
        console.log('📝 步骤2: 生成授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();
        console.log('✅ 授权 URL 生成完成');
        console.log('');
        console.log('🔗 生成的授权 URL:');
        console.log(authUrl);
        console.log('');

        // 步骤3: 显示 URL 组成部分
        console.log('📝 步骤3: 分析 URL 组成部分');
        const url = new URL(authUrl);
        console.log('🔍 URL 分析:');
        console.log(`  📍 基础 URL: ${url.origin}${url.pathname}`);
        console.log(`  🔑 客户端 ID: ${url.searchParams.get('client_id')}`);
        console.log(`  🎯 响应类型: ${url.searchParams.get('response_type')}`);
        console.log(`  🛡️ 代码挑战: ${url.searchParams.get('code_challenge')?.substring(0, 20)}...`);
        console.log(`  🎲 状态参数: ${url.searchParams.get('state')}`);
        console.log(`  🔐 提示模式: ${url.searchParams.get('prompt')}`);
        console.log('');

        // 步骤4: 模拟授权码处理
        console.log('📝 步骤4: 模拟授权码处理');
        console.log('💡 在实际使用中，这个授权码会从剪贴板获取');
        
        const mockAuthCode = JSON.stringify({
            code: 'mock_auth_code_' + Date.now(),
            state: augmentAuth.oauthState.state,
            tenant_url: 'https://demo.augmentcode.com/'
        }, null, 2);

        console.log('📋 模拟的授权码 JSON:');
        console.log(mockAuthCode);
        console.log('');

        // 步骤5: 解析授权码
        console.log('📝 步骤5: 解析授权码');
        const parsedCode = augmentAuth.parseAuthCode(mockAuthCode);
        console.log('✅ 授权码解析成功');
        console.log('');

        // 步骤6: 模拟令牌响应
        console.log('📝 步骤6: 模拟访问令牌获取');
        console.log('💡 在实际使用中，这里会调用 Augment API 获取真实令牌');
        
        const mockTokenResponse = {
            access_token: 'demo_access_token_' + Date.now(),
            tenant_url: parsedCode.tenant_url,
            oauth_state: augmentAuth.oauthState,
            parsed_code: parsedCode
        };

        console.log('🔑 模拟的令牌响应:');
        console.log(`  - 访问令牌: ${mockTokenResponse.access_token.substring(0, 30)}...`);
        console.log(`  - 租户 URL: ${mockTokenResponse.tenant_url}`);
        console.log('');

        // 步骤7: 保存令牌
        console.log('📝 步骤7: 保存令牌到存储');
        const tokenId = tokenStorage.addToken(mockTokenResponse, {
            description: 'Demo token for testing',
            user_agent: 'demo-augment-auth',
            session_id: 'demo_session_' + Date.now()
        });

        console.log('✅ 令牌保存成功');
        console.log(`💾 令牌 ID: ${tokenId}`);
        console.log('');

        // 步骤8: 验证保存的令牌
        console.log('📝 步骤8: 验证保存的令牌');
        const savedToken = tokenStorage.getTokenById(tokenId);
        if (savedToken) {
            console.log('✅ 令牌验证成功');
            console.log('📊 令牌详情:');
            console.log(`  - ID: ${savedToken.id}`);
            console.log(`  - 创建时间: ${savedToken.createdTime}`);
            console.log(`  - 租户 URL: ${savedToken.tenant_url}`);
            console.log(`  - 描述: ${savedToken.description}`);
        }
        console.log('');

        // 步骤9: 显示统计信息
        console.log('📝 步骤9: 显示令牌统计');
        const stats = tokenStorage.getStats();
        console.log('');

        // 步骤10: 总结
        console.log('🎉 演示完成！');
        console.log('='.repeat(50));
        console.log('');
        console.log('📋 演示总结:');
        console.log('  ✅ 成功生成了符合 OAuth 2.0 PKCE 标准的授权 URL');
        console.log('  ✅ 正确解析了授权码 JSON 格式');
        console.log('  ✅ 模拟了访问令牌获取流程');
        console.log('  ✅ 成功保存令牌到本地存储');
        console.log('  ✅ 验证了令牌读取和统计功能');
        console.log('');
        console.log('🚀 实际使用步骤:');
        console.log('  1. 运行: node run-email-verification.js');
        console.log('  2. 系统自动生成授权 URL 并打开浏览器');
        console.log('  3. 完成邮箱验证后点击 "Copy to Clipboard"');
        console.log('  4. 系统自动解析授权码并获取访问令牌');
        console.log('  5. 令牌自动保存到 tokens.json 文件');
        console.log('');
        console.log('📁 相关文件:');
        console.log('  - tokens.json: 令牌存储文件');
        console.log('  - image/: 截图和调试文件');
        console.log('  - AUGMENT-AUTH-README.md: 详细使用说明');

    } catch (error) {
        console.error('');
        console.error('💥 演示过程中出现错误:', error.message);
        console.error('📋 错误详情:', error.stack);
    }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
    demoAugmentAuth();
}

module.exports = demoAugmentAuth;
