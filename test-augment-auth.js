const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');

/**
 * 测试 Augment 授权功能
 */
async function testAugmentAuth() {
    console.log('🧪 开始测试 Augment 授权功能');
    console.log('');

    try {
        // 测试1: 创建 AugmentAuth 实例
        console.log('📝 测试1: 创建 AugmentAuth 实例');
        const augmentAuth = new AugmentAuth();
        console.log('✅ AugmentAuth 实例创建成功');
        console.log('');

        // 测试2: 生成授权 URL
        console.log('📝 测试2: 生成授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();
        console.log('✅ 授权 URL 生成成功');
        console.log(`🔗 URL: ${authUrl}`);
        console.log('');

        // 测试3: 验证 URL 格式
        console.log('📝 测试3: 验证 URL 格式');
        const url = new URL(authUrl);
        const expectedParams = ['response_type', 'code_challenge', 'client_id', 'state', 'prompt'];
        const actualParams = Array.from(url.searchParams.keys());
        
        console.log('🔍 URL 参数检查:');
        expectedParams.forEach(param => {
            const hasParam = url.searchParams.has(param);
            console.log(`  - ${param}: ${hasParam ? '✅' : '❌'} ${hasParam ? url.searchParams.get(param) : '缺失'}`);
        });
        console.log('');

        // 测试4: 创建 TokenStorage 实例
        console.log('📝 测试4: 创建 TokenStorage 实例');
        const tokenStorage = new TokenStorage('./test-tokens.json');
        console.log('✅ TokenStorage 实例创建成功');
        console.log('');

        // 测试5: 测试令牌存储功能
        console.log('📝 测试5: 测试令牌存储功能');
        const mockTokenResponse = {
            access_token: 'test_access_token_' + Date.now(),
            tenant_url: 'https://test.augmentcode.com/',
            oauth_state: augmentAuth.oauthState,
            parsed_code: {
                code: 'test_code',
                state: augmentAuth.oauthState.state,
                tenant_url: 'https://test.augmentcode.com/'
            }
        };

        const tokenId = tokenStorage.addToken(mockTokenResponse, {
            description: 'Test token for unit testing',
            user_agent: 'test-augment-auth',
            session_id: 'test_session_' + Date.now()
        });

        console.log('✅ 测试令牌添加成功');
        console.log(`💾 令牌 ID: ${tokenId}`);
        console.log('');

        // 测试6: 读取令牌
        console.log('📝 测试6: 读取令牌');
        const savedToken = tokenStorage.getTokenById(tokenId);
        if (savedToken) {
            console.log('✅ 令牌读取成功');
            console.log(`📖 令牌信息:`);
            console.log(`  - ID: ${savedToken.id}`);
            console.log(`  - 创建时间: ${savedToken.createdTime}`);
            console.log(`  - 租户 URL: ${savedToken.tenant_url}`);
            console.log(`  - 访问令牌: ${savedToken.access_token.substring(0, 20)}...`);
        } else {
            console.log('❌ 令牌读取失败');
        }
        console.log('');

        // 测试7: 获取统计信息
        console.log('📝 测试7: 获取统计信息');
        const stats = tokenStorage.getStats();
        console.log('✅ 统计信息获取成功');
        console.log('');

        // 测试8: 模拟授权码解析（使用模拟数据）
        console.log('📝 测试8: 模拟授权码解析');
        const mockAuthCode = JSON.stringify({
            code: 'mock_authorization_code_12345',
            state: augmentAuth.oauthState.state,
            tenant_url: 'https://mock.augmentcode.com/'
        });

        try {
            const parsedCode = augmentAuth.parseAuthCode(mockAuthCode);
            console.log('✅ 授权码解析成功');
            console.log(`📋 解析结果:`);
            console.log(`  - Code: ${parsedCode.code.substring(0, 20)}...`);
            console.log(`  - State: ${parsedCode.state}`);
            console.log(`  - Tenant URL: ${parsedCode.tenant_url}`);
        } catch (error) {
            console.log('❌ 授权码解析失败:', error.message);
        }
        console.log('');

        console.log('🎉 所有测试完成！');
        console.log('');
        console.log('📋 测试总结:');
        console.log('  ✅ AugmentAuth 类功能正常');
        console.log('  ✅ TokenStorage 类功能正常');
        console.log('  ✅ 授权 URL 生成正确');
        console.log('  ✅ 令牌存储和读取正常');
        console.log('  ✅ 授权码解析功能正常');
        console.log('');
        console.log('🚀 可以开始使用完整的授权流程！');
        console.log('');
        console.log('📝 使用方法:');
        console.log('  1. 运行: node run-email-verification.js');
        console.log('  2. 系统会自动生成授权 URL 并执行邮箱验证');
        console.log('  3. 获取到授权码后会自动获取访问令牌');
        console.log('  4. 令牌会保存到 tokens.json 文件中');

    } catch (error) {
        console.error('');
        console.error('💥 测试失败:', error.message);
        console.error('📋 错误详情:', error.stack);
        process.exit(1);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    testAugmentAuth();
}

module.exports = testAugmentAuth;
