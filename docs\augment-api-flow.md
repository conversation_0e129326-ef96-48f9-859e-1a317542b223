# Augment API 授权流程核心逻辑

## 概述

Augment 使用 OAuth 2.0 + PKCE 授权流程，涉及两个主要 API 调用：
1. 生成授权 URL
2. 使用授权码换取访问令牌

## 步骤 1: 生成授权 URL

### 1.1 生成 PKCE 参数

```javascript
// 生成 32 字节随机数据作为 code_verifier
const codeVerifier = base64UrlEncode(randomBytes(32))

// 生成 code_challenge (SHA256 hash of code_verifier)
const codeChallenge = base64UrlEncode(sha256(codeVerifier))

// 生成随机 state 参数
const state = base64UrlEncode(randomBytes(8))
```

### 1.2 构建授权 URL

**基础信息:**
- **授权服务器**: `https://auth.augmentcode.com`
- **客户端 ID**: `v`

**授权 URL 格式:**
```
https://auth.augmentcode.com/authorize?response_type=code&code_challenge={code_challenge}&client_id=v&state={state}&prompt=login
```

**参数说明:**
- `response_type=code`: OAuth 2.0 授权码模式
- `code_challenge`: PKCE 挑战码
- `client_id=v`: Augment 客户端 ID
- `state`: 防 CSRF 攻击的随机值
- `prompt=login`: 强制用户重新登录

**示例 URL:**
```
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM&client_id=v&state=GMMhmHCXhWEv&prompt=login
```

## 步骤 2: 用户授权

### 2.1 用户操作流程

1. **访问授权 URL** → 跳转到 Augment 登录页面
2. **输入用户名密码** → 完成身份验证
3. **获取授权响应** → 浏览器显示 JSON 格式的授权码

### 2.2 授权响应格式

**成功响应示例:**
```json
{
  "code": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
  "state": "GMMhmHCXhWEv",
  "tenant_url": "https://your-tenant.augmentcode.com/"
}
```

**字段说明:**
- `code`: 授权码，用于换取访问令牌
- `state`: 状态值，需要与之前生成的 state 匹配
- `tenant_url`: 用户的 Augment 租户地址

## 步骤 3: 换取访问令牌

### 3.1 令牌端点 API 调用

**API 端点:**
```
POST {tenant_url}token
```

**请求头:**
```http
Content-Type: application/json
```

**请求体:**
```json
{
  "grant_type": "authorization_code",
  "client_id": "v",
  "code_verifier": "{code_verifier}",
  "redirect_uri": "",
  "code": "{authorization_code}"
}
```

**参数说明:**
- `grant_type`: 固定值 "authorization_code"
- `client_id`: 固定值 "v"
- `code_verifier`: 步骤 1 生成的 PKCE 验证码
- `redirect_uri`: 桌面应用使用空字符串
- `code`: 从授权响应中获取的授权码

### 3.2 完整 API 请求示例

```http
POST https://your-tenant.augmentcode.com/token
Content-Type: application/json

{
  "grant_type": "authorization_code",
  "client_id": "v",
  "code_verifier": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk",
  "redirect_uri": "",
  "code": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3.3 令牌响应

**成功响应:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

**错误响应示例:**
```json
{
  "error": "invalid_grant",
  "error_description": "The provided authorization grant is invalid, expired, revoked, does not match the redirection URI used in the authorization request, or was issued to another client."
}
```

## 完整流程总结

### API 调用序列

```
1. 生成 PKCE 参数 (本地计算)
   ├── code_verifier = base64UrlEncode(randomBytes(32))
   ├── code_challenge = base64UrlEncode(sha256(code_verifier))
   └── state = base64UrlEncode(randomBytes(8))

2. 构建授权 URL (本地拼接)
   └── https://auth.augmentcode.com/authorize?...

3. 用户访问授权 URL (浏览器操作)
   ├── 用户登录
   └── 获取授权码 JSON

4. 调用令牌 API (HTTP POST)
   ├── POST {tenant_url}token
   ├── 发送: grant_type, client_id, code_verifier, code
   └── 接收: access_token
```

### 关键数据流

```
输入: 无
  ↓
生成: code_verifier, code_challenge, state
  ↓
输出: 授权 URL
  ↓
用户操作: 访问 URL, 登录, 复制授权码
  ↓
输入: 授权码 JSON {code, state, tenant_url}
  ↓
API 调用: POST {tenant_url}token
  ↓
输出: access_token
```

### 核心配置

```javascript
const CONFIG = {
  AUTH_SERVER: "https://auth.augmentcode.com",
  CLIENT_ID: "v",
  AUTHORIZE_ENDPOINT: "/authorize",
  TOKEN_ENDPOINT: "token"  // 相对于 tenant_url
}
```

### 安全要点

1. **PKCE 验证**: code_verifier 必须与 code_challenge 匹配
2. **State 验证**: 返回的 state 必须与发送的 state 匹配
3. **一次性使用**: 授权码只能使用一次
4. **HTTPS 传输**: 所有 API 调用必须使用 HTTPS
5. **令牌存储**: access_token 应安全存储，避免泄露

### 错误处理

**常见错误码:**
- `invalid_request`: 请求参数错误
- `invalid_client`: 客户端认证失败
- `invalid_grant`: 授权码无效或过期
- `unsupported_grant_type`: 不支持的授权类型
- `invalid_scope`: 请求的权限范围无效

**重试策略:**
- 网络错误: 可重试
- 4xx 错误: 不可重试，需要重新授权
- 5xx 错误: 可短暂重试
