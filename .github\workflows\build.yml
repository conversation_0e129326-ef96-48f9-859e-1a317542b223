name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: macos-latest
            target: aarch64-apple-darwin
            name: Apple Silicon
            rust-target: aarch64-apple-darwin
          - platform: macos-latest
            target: x86_64-apple-darwin
            name: Intel
            rust-target: x86_64-apple-darwin
          - platform: windows-latest
            target: x86_64-pc-windows-msvc
            name: Windows
            rust-target: x86_64-pc-windows-msvc

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4



      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.rust-target }}

      - name: Install Rust target (macOS only)
        if: matrix.platform == 'macos-latest'
        run: rustup target add ${{ matrix.rust-target }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: Sync node version and setup cache
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Install frontend dependencies
        run: npm ci

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tagName: ${{ github.ref_name }}
          releaseName: 'OAuth Token Generator v${{ github.ref_name }} (${{ matrix.name }})'
          releaseBody: 'See the assets to download and install this version.'
          releaseDraft: true
          prerelease: false
          args: --verbose --target ${{ matrix.target }}
          artifactName: ${{ matrix.name }}
