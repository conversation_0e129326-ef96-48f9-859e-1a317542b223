* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    height: 100vh;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

#app {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 30px;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.2em;
    font-weight: 300;
}

h2 {
    color: #555;
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 500;
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn.primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.btn.secondary {
    background: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.btn.secondary:hover {
    background: #e0e0e0;
    transform: translateY(-1px);
}

.url-section {
    margin-top: 15px;
}

.url-container, .token-container {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.token-section {
    margin-top: 15px;
}

input[type="text"], textarea {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Courier New', monospace;
    width: 100%;
}

textarea {
    resize: vertical;
    min-height: 100px;
    font-family: 'Courier New', monospace;
    margin-bottom: 15px;
}

.button-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
}

input[readonly] {
    background: #f9f9f9;
    color: #666;
}

.result-container {
    margin-bottom: 15px;
}

.result-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.hidden {
    display: none;
}



/* Loading animation */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Token Generator Main Page Styles */
.token-generator-main {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.generator-header {
    text-align: center;
    margin-bottom: 30px;
}

.generator-header h2 {
    color: #1f2937;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.generator-header p {
    color: #6b7280;
    font-size: 1.1rem;
}

.generator-body {
    max-width: 600px;
    margin: 0 auto;
}

.generator-body .section {
    margin-bottom: 30px;
    padding: 24px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: #f9fafb;
}

.generator-body .section h3 {
    color: #374151;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.url-section {
    margin-top: 20px;
}

.url-section label {
    display: block;
    color: #374151;
    font-weight: 500;
    margin-bottom: 8px;
}

.generator-body .url-container, .generator-body .token-container {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.generator-body .url-container input, .generator-body .token-container input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.button-container {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.token-section {
    margin-top: 20px;
}

.result-container {
    margin-bottom: 16px;
}

.result-container label {
    display: block;
    color: #374151;
    font-weight: 500;
    margin-bottom: 8px;
}

.generator-body textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-family: monospace;
    resize: vertical;
    background: white;
}

.generator-body textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn.success {
    background: #10b981;
    color: white;
}

.btn.success:hover {
    background: #059669;
}


