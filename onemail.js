const axios = require('axios');
require('dotenv').config();

class OneMailHandler {
    constructor() {
        this.apiUrl = process.env.ONE_MAIL_API_URL || 'http://localhost:9042/api/v1';
        this.apiPassword = process.env.API_AUTH_PASSWORD || 'your-api-password-here';
        this.currentEmail = null;
    }

    log(message) {
        console.log(`[ONE_MAIL] ${new Date().toLocaleTimeString()} - ${message}`);
    }

    async generateEmail() {
        try {
            this.log('正在生成新的临时邮箱...');
            
            const response = await axios.post(`${this.apiUrl}/generate-email`, {}, {
                headers: {
                    'Authorization': `Bearer ${this.apiPassword}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.data && response.data.success && response.data.email) {
                this.currentEmail = response.data.email;
                this.log(`✅ 邮箱生成成功: ${this.currentEmail}`);
                return this.currentEmail;
            } else {
                throw new Error('API返回格式错误');
            }
        } catch (error) {
            this.log(`❌ 邮箱生成失败: ${error.message}`);
            if (error.response) {
                this.log(`API响应: ${JSON.stringify(error.response.data)}`);
            }
            throw error;
        }
    }

    async getVerificationCode(email = null, timeoutMinutes = 2) {
        const targetEmail = email || this.currentEmail;
        if (!targetEmail) {
            throw new Error('没有可用的邮箱地址');
        }

        this.log(`开始获取验证码，邮箱: ${targetEmail}`);
        this.log(`超时时间: ${timeoutMinutes} 分钟`);

        const startTime = Date.now();
        const timeoutMs = timeoutMinutes * 60 * 1000;
        let attemptCount = 0;

        while (Date.now() - startTime < timeoutMs) {
            attemptCount++;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            
            try {
                this.log(`第 ${attemptCount} 次尝试 (${elapsed}s)`);
                
                const response = await axios.get(`${this.apiUrl}/verification-codes`, {
                    params: {
                        email: targetEmail,
                        latest: true,
                        timeWindow: 5
                    },
                    headers: {
                        'Authorization': `Bearer ${this.apiPassword}`
                    },
                    timeout: 10000
                });

                if (response.data && response.data.success && response.data.latest_code) {
                    const codeData = response.data.latest_code;
                    this.log(`✅ 找到验证码: ${codeData.code}`);
                    this.log(`邮件主题: ${codeData.subject}`);
                    this.log(`接收时间: ${codeData.date}`);
                    return codeData.code;
                } else {
                    this.log('暂未收到验证码');
                }
            } catch (error) {
                this.log(`获取验证码失败: ${error.message}`);
                if (error.response) {
                    this.log(`API响应: ${JSON.stringify(error.response.data)}`);
                }
            }

            // 等待5秒后重试
            await this.wait(5000);
        }

        throw new Error(`超时 ${timeoutMinutes} 分钟未收到验证码`);
    }

    async getCurrentEmail() {
        return this.currentEmail;
    }

    async clearCurrentEmail() {
        this.currentEmail = null;
        this.log("当前邮箱信息已清除");
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 测试API连接
    async testConnection() {
        try {
            this.log('测试API连接...');
            
            const response = await axios.get(`${this.apiUrl}/docs`, {
                timeout: 5000
            });

            if (response.status === 200) {
                this.log('✅ API连接正常');
                return true;
            }
        } catch (error) {
            this.log(`❌ API连接失败: ${error.message}`);
            return false;
        }
    }
}

module.exports = OneMailHandler;
