# Augment OAuth 授权流程详解

## 概述

Augment Token Manager 实现了完整的 OAuth 2.0 授权码模式 + PKCE (Proof Key for Code Exchange) 流程，用于安全地获取 Augment Code 访问令牌。

## 核心流程

### 步骤 1: 生成 Augment 授权 URL

#### 1.1 前端触发

**UI 交互:**
```vue
<button @click="generateAuthUrl" :disabled="isGenerating">
  生成Augment授权URL
</button>
```

**前端函数:**
```javascript
const generateAuthUrl = async () => {
  isGenerating.value = true
  try {
    const url = await invoke('generate_augment_auth_url')
    authUrl.value = url
  } catch (error) {
    showStatus(`错误: ${error}`, 'error')
  } finally {
    isGenerating.value = false
  }
}
```

#### 1.2 后端 API 调用

**Tauri Command:**
```rust
#[tauri::command]
async fn generate_augment_auth_url(state: State<'_, AppState>) -> Result<String, String> {
    let augment_oauth_state = create_augment_oauth_state();
    let auth_url = generate_augment_authorize_url(&augment_oauth_state)
        .map_err(|e| format!("Failed to generate Augment auth URL: {}", e))?;
    
    // Store the Augment OAuth state
    *state.augment_oauth_state.lock().unwrap() = Some(augment_oauth_state);
    
    Ok(auth_url)
}
```

#### 1.3 PKCE 参数生成

**OAuth 状态创建:**
```rust
pub fn create_augment_oauth_state() -> AugmentOAuthState {
    // 1. 生成 32 字节随机 code_verifier
    let code_verifier_bytes = generate_random_bytes(32);
    let code_verifier = base64_url_encode(&code_verifier_bytes);
    
    // 2. 生成 code_challenge (SHA256 hash of code_verifier)
    let code_challenge_bytes = sha256_hash(code_verifier.as_bytes());
    let code_challenge = base64_url_encode(&code_challenge_bytes);
    
    // 3. 生成随机 state 参数
    let state_bytes = generate_random_bytes(8);
    let state = base64_url_encode(&state_bytes);
    
    // 4. 记录创建时间
    let creation_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    
    AugmentOAuthState {
        code_verifier,    // 保存在本地，用于后续验证
        code_challenge,   // 发送给授权服务器
        state,           // 防 CSRF 攻击
        creation_time,   // 验证请求时效性
    }
}
```

#### 1.4 授权 URL 构建

**URL 生成逻辑:**
```rust
pub fn generate_augment_authorize_url(oauth_state: &AugmentOAuthState) -> Result<String, Box<dyn std::error::Error>> {
    let mut url = Url::parse(&format!("{}/authorize", AUTH_BASE_URL))?;
    
    url.query_pairs_mut()
        .append_pair("response_type", "code")                    // OAuth 2.0 授权码模式
        .append_pair("code_challenge", &oauth_state.code_challenge) // PKCE 挑战码
        .append_pair("client_id", CLIENT_ID)                     // 客户端 ID: "v"
        .append_pair("state", &oauth_state.state)                // 防 CSRF 状态码
        .append_pair("prompt", "login");                         // 强制用户登录
    
    Ok(url.to_string())
}
```

**生成的 URL 示例:**
```
https://auth.augmentcode.com/authorize?response_type=code&code_challenge=E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM&client_id=v&state=GMMhmHCXhWEv&prompt=login
```

#### 1.5 关键常量

```rust
const CLIENT_ID: &str = "v";                              // Augment 客户端 ID
const AUTH_BASE_URL: &str = "https://auth.augmentcode.com"; // 授权服务器地址
```

### 步骤 2: 输入授权码并获取访问令牌

#### 2.1 用户授权流程

1. **用户访问授权 URL** → 跳转到 Augment 登录页面
2. **用户输入凭据** → 完成身份验证
3. **授权服务器返回授权码** → JSON 格式响应

**授权码 JSON 格式:**
```json
{
  "code": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "state": "GMMhmHCXhWEv",
  "tenant_url": "https://your-tenant.augmentcode.com/"
}
```

#### 2.2 前端处理授权码

**UI 输入:**
```vue
<textarea
  v-model="authCode"
  placeholder="在此粘贴授权码JSON..."
  rows="4"
></textarea>
<button @click="getAccessToken" :disabled="!canGetToken">
  获取访问令牌
</button>
```

**前端函数:**
```javascript
const getAccessToken = async () => {
  isGettingToken.value = true
  showStatus('正在获取访问令牌...', 'info')

  try {
    const result = await invoke('get_augment_token', { code: authCode.value })
    tokenResult.value = result
    showStatus('访问令牌获取成功!', 'success')
  } catch (error) {
    showStatus(`错误: ${error}`, 'error')
  } finally {
    isGettingToken.value = false
  }
}
```

#### 2.3 后端令牌交换

**Tauri Command:**
```rust
#[tauri::command]
async fn get_augment_token(code: String, state: State<'_, AppState>) -> Result<AugmentTokenResponse, String> {
    // 1. 获取之前保存的 OAuth 状态
    let augment_oauth_state = {
        let guard = state.augment_oauth_state.lock().unwrap();
        guard.clone()
            .ok_or("No Augment OAuth state found. Please generate auth URL first.")?
    };

    // 2. 完成 OAuth 流程
    complete_augment_oauth_flow(&augment_oauth_state, &code)
        .await
        .map_err(|e| format!("Failed to complete Augment OAuth flow: {}", e))
}
```

#### 2.4 授权码解析

**JSON 解析:**
```rust
pub fn parse_code(code: &str) -> Result<ParsedCode, Box<dyn std::error::Error>> {
    let parsed: ParsedCode = serde_json::from_str(code)?;
    Ok(parsed)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ParsedCode {
    pub code: String,        // 授权码
    pub state: String,       // 状态值
    pub tenant_url: String,  // 租户 URL
}
```

#### 2.5 访问令牌请求

**核心令牌交换逻辑:**
```rust
pub async fn get_augment_access_token(
    tenant_url: &str,
    code_verifier: &str,
    code: &str,
) -> Result<String, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    // 构建请求参数
    let mut data = HashMap::new();
    data.insert("grant_type", "authorization_code");  // OAuth 2.0 授权码模式
    data.insert("client_id", CLIENT_ID);              // 客户端 ID
    data.insert("code_verifier", code_verifier);      // PKCE 验证码
    data.insert("redirect_uri", "");                  // 桌面应用无需重定向
    data.insert("code", code);                        // 授权码
    
    // 发送 POST 请求到令牌端点
    let token_url = format!("{}token", tenant_url);
    let response = client
        .post(&token_url)
        .json(&data)
        .send()
        .await?;

    // 解析响应获取访问令牌
    let token_response: TokenApiResponse = response.json().await?;
    Ok(token_response.access_token)
}
```

**API 请求示例:**
```http
POST https://your-tenant.augmentcode.com/token
Content-Type: application/json

{
  "grant_type": "authorization_code",
  "client_id": "v",
  "code_verifier": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk",
  "redirect_uri": "",
  "code": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**API 响应示例:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

## API 调用时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端 Vue
    participant Backend as 后端 Rust
    participant AuthServer as Augment 授权服务器
    participant TokenEndpoint as 令牌端点

    Note over User,TokenEndpoint: 步骤 1: 生成授权 URL
    User->>Frontend: 点击"生成授权URL"
    Frontend->>Backend: invoke('generate_augment_auth_url')
    Backend->>Backend: create_augment_oauth_state()
    Note over Backend: 生成 code_verifier, code_challenge, state
    Backend->>Backend: generate_augment_authorize_url()
    Backend->>Frontend: 返回授权 URL
    Frontend->>User: 显示授权 URL

    Note over User,TokenEndpoint: 用户授权流程
    User->>AuthServer: 访问授权 URL
    AuthServer->>User: 显示登录页面
    User->>AuthServer: 输入凭据
    AuthServer->>User: 返回授权码 JSON

    Note over User,TokenEndpoint: 步骤 2: 获取访问令牌
    User->>Frontend: 粘贴授权码并点击"获取令牌"
    Frontend->>Backend: invoke('get_augment_token', {code})
    Backend->>Backend: parse_code(code)
    Note over Backend: 解析 code, state, tenant_url
    Backend->>TokenEndpoint: POST {tenant_url}token
    Note over Backend: 发送 grant_type, client_id, code_verifier, code
    TokenEndpoint->>Backend: 返回 access_token
    Backend->>Frontend: 返回 {access_token, tenant_url}
    Frontend->>User: 显示令牌结果
```

## 安全特性

### PKCE (Proof Key for Code Exchange)
- **code_verifier**: 43-128 字符的随机字符串，保存在客户端
- **code_challenge**: code_verifier 的 SHA256 哈希值，发送给授权服务器
- **防止授权码拦截攻击**: 即使攻击者获得授权码，没有 code_verifier 也无法换取令牌

### State 参数
- **随机生成**: 8 字节随机数据，Base64 URL 编码
- **防 CSRF 攻击**: 验证授权响应的合法性
- **一次性使用**: 每次授权流程生成新的 state 值

### 本地状态管理
- **内存存储**: OAuth 状态保存在应用内存中
- **生命周期管理**: 状态与授权流程绑定，完成后可清理
- **线程安全**: 使用 Mutex 保护共享状态

## 错误处理

### 常见错误场景
1. **OAuth 状态丢失**: "No Augment OAuth state found"
2. **授权码格式错误**: JSON 解析失败
3. **网络请求失败**: 无法连接到令牌端点
4. **令牌端点错误**: 4xx/5xx HTTP 状态码
5. **PKCE 验证失败**: code_verifier 不匹配

### 错误处理策略
- **前端**: 显示用户友好的错误消息
- **后端**: 详细的错误日志和调试信息
- **重试机制**: 网络错误时的自动重试
- **状态重置**: 错误后清理无效状态
