# Augment 授权自动化集成

本项目已集成了 Augment OAuth 授权流程，可以自动生成授权 URL、获取访问令牌并保存到本地 JSON 文件中。

## 🚀 功能特性

- ✅ 自动生成 Augment 授权 URL（替换硬编码的 LINK_TO_TEST）
- ✅ 完整的 OAuth 2.0 PKCE 流程实现
- ✅ 自动解析授权码并获取访问令牌
- ✅ 令牌数据持久化存储到 JSON 文件
- ✅ 详细的调试日志和错误处理
- ✅ 模块化设计，易于维护和扩展

## 📁 新增文件

### 核心模块
- `augment-auth.js` - Augment OAuth 授权处理类
- `token-storage.js` - 令牌存储管理类
- `test-augment-auth.js` - 功能测试脚本

### 数据文件
- `tokens.json` - 令牌存储文件（自动生成）

## 🔧 使用方法

### 1. 运行完整的授权和邮箱验证流程

```bash
node run-email-verification.js
```

这个命令会：
1. 🔐 自动生成 Augment 授权 URL
2. 📧 使用 One Mail API 生成临时邮箱
3. 🤖 自动填写邮箱并获取验证码
4. 📋 点击 "Copy to Clipboard" 按钮获取授权码
5. 🔑 自动解析授权码并获取访问令牌
6. 💾 保存令牌到 `tokens.json` 文件

### 2. 测试授权功能

```bash
node test-augment-auth.js
```

这个命令会测试所有授权相关功能，确保一切正常工作。

## 📊 令牌数据结构

保存在 `tokens.json` 中的每个令牌包含以下信息：

```json
{
  "id": "uuid-string",
  "createdTime": "2025-08-15T10:17:03.260Z",
  "createdTimestamp": 1723717023260,
  "access_token": "actual_access_token",
  "tenant_url": "https://tenant.augmentcode.com/",
  "oauth_state": {
    "codeVerifier": "...",
    "codeChallenge": "...",
    "state": "...",
    "creationTime": 1723717023260
  },
  "parsed_code": {
    "code": "authorization_code",
    "state": "state_value",
    "tenant_url": "https://tenant.augmentcode.com/"
  },
  "description": "Auto-generated via email verification",
  "metadata": {
    "user_agent": "augment-auto-email-verification",
    "session_id": "session_1723717023260"
  }
}
```

## 🔍 调试和日志

### 调试信息
- 每个步骤都有详细的 console.log 输出
- 授权 URL 生成过程的参数显示
- HTTP 请求和响应的完整日志
- 令牌存储操作的确认信息

### 错误处理
- 网络请求失败时的重试机制
- 授权码解析错误的详细提示
- 文件操作失败的错误恢复
- State 验证失败的安全检查

## 🛠️ 技术实现

### OAuth 2.0 PKCE 流程
1. **生成 Code Verifier**: 32字节随机数，Base64URL 编码
2. **生成 Code Challenge**: Code Verifier 的 SHA256 哈希，Base64URL 编码
3. **生成 State**: 8字节随机数，Base64URL 编码，用于 CSRF 保护
4. **构建授权 URL**: 包含所有必要参数的完整 URL
5. **交换访问令牌**: 使用授权码和 Code Verifier 获取访问令牌

### 安全特性
- ✅ PKCE (Proof Key for Code Exchange) 实现
- ✅ State 参数验证防止 CSRF 攻击
- ✅ 随机数生成使用加密安全的方法
- ✅ 敏感信息在日志中部分隐藏

## 📝 配置说明

### 环境变量
- `LINK_TO_TEST` - 现在会被自动生成的授权 URL 覆盖

### 常量配置
在 `augment-auth.js` 中：
```javascript
this.CLIENT_ID = 'v';  // Augment 客户端 ID
this.AUTH_BASE_URL = 'https://auth.augmentcode.com';  // 授权服务器地址
```

## 🔄 与现有流程的集成

### 修改的文件
1. **run-email-verification.js**
   - 导入新的授权模块
   - 在开始时生成授权 URL
   - 在获取剪贴板内容后处理令牌

2. **index.js**
   - 修改 `handleEmailVerificationWithOneMailAPI` 方法
   - 返回剪贴板内容供后续处理

### 保持兼容性
- 现有的邮箱验证流程完全保持不变
- 只是在成功获取授权码后增加了令牌处理
- 如果令牌处理失败，不会影响邮箱验证的成功

## 🎯 下一步扩展

可以考虑的功能扩展：
- 🔄 令牌自动刷新机制
- 📊 令牌使用统计和分析
- 🔐 令牌加密存储
- 🌐 多租户支持
- 📱 Web 界面管理令牌
- 🔔 令牌过期提醒

## ❓ 常见问题

### Q: 如果授权失败怎么办？
A: 系统会显示详细的错误信息，可以查看日志进行调试。授权失败不会影响邮箱验证流程的其他部分。

### Q: 令牌文件在哪里？
A: 令牌保存在项目根目录的 `tokens.json` 文件中。

### Q: 如何查看已保存的令牌？
A: 可以直接查看 `tokens.json` 文件，或者使用 TokenStorage 类的方法程序化访问。

### Q: 令牌会过期吗？
A: 令牌本身可能有过期时间，但系统会保存所有历史令牌。可以使用 TokenStorage 的 cleanup 方法清理过期令牌。

## 📞 支持

如果遇到问题，请检查：
1. 网络连接是否正常
2. Augment 授权服务是否可访问
3. 查看详细的控制台日志
4. 运行测试脚本验证功能
