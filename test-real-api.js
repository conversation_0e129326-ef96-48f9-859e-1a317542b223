const AugmentAuth = require('./augment-auth.js');

/**
 * 测试真实的 Augment API 调用
 * 这个脚本展示了如何使用真实的授权码调用真实的 API
 */
async function testRealAPI() {
    console.log('🧪 测试真实的 Augment API 调用');
    console.log('='.repeat(50));
    console.log('');

    try {
        // 步骤1: 创建授权实例
        console.log('📝 步骤1: 创建授权实例');
        const augmentAuth = new AugmentAuth();
        console.log('✅ 实例创建完成');
        console.log('');

        // 步骤2: 生成真实的授权 URL
        console.log('📝 步骤2: 生成真实的授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();
        console.log('✅ 真实授权 URL 生成完成');
        console.log('');
        console.log('🔗 请在浏览器中访问以下 URL 完成授权:');
        console.log(authUrl);
        console.log('');

        // 步骤3: 说明如何获取授权码
        console.log('📝 步骤3: 获取授权码的步骤');
        console.log('🔍 请按照以下步骤操作:');
        console.log('  1. 复制上面的 URL 到浏览器');
        console.log('  2. 完成邮箱验证流程');
        console.log('  3. 点击 "Copy to Clipboard" 按钮');
        console.log('  4. 将剪贴板内容粘贴到下面的提示中');
        console.log('');

        // 步骤4: 等待用户输入授权码
        console.log('📝 步骤4: 等待授权码输入');
        console.log('💡 如果您想测试真实的 API 调用，请:');
        console.log('   - 运行: node run-email-verification.js');
        console.log('   - 这会自动完成整个流程并调用真实的 API');
        console.log('');

        // 步骤5: 展示真实 API 调用的代码
        console.log('📝 步骤5: 真实 API 调用代码示例');
        console.log('🔍 当您有真实的授权码时，代码会这样调用:');
        console.log('');
        console.log('```javascript');
        console.log('// 真实的授权码 JSON (从剪贴板获取)');
        console.log('const realAuthCode = `{');
        console.log('  "code": "real_authorization_code_from_augment",');
        console.log('  "state": "' + augmentAuth.oauthState.state + '",');
        console.log('  "tenant_url": "https://your-tenant.augmentcode.com/"');
        console.log('}`;');
        console.log('');
        console.log('// 调用真实的 API');
        console.log('const tokenResponse = await augmentAuth.completeOAuthFlow(realAuthCode);');
        console.log('// 这会向 https://your-tenant.augmentcode.com/token 发送 HTTPS 请求');
        console.log('```');
        console.log('');

        // 步骤6: 展示 API 请求详情
        console.log('📝 步骤6: 真实 API 请求详情');
        console.log('🔍 API 请求会包含以下信息:');
        console.log('');
        console.log('📡 请求 URL: {tenant_url}token');
        console.log('📡 请求方法: POST');
        console.log('📡 请求头: Content-Type: application/json');
        console.log('📡 请求体:');
        console.log('  {');
        console.log('    "grant_type": "authorization_code",');
        console.log('    "client_id": "v",');
        console.log('    "code_verifier": "' + augmentAuth.oauthState.codeVerifier.substring(0, 20) + '...",');
        console.log('    "redirect_uri": "",');
        console.log('    "code": "real_authorization_code"');
        console.log('  }');
        console.log('');

        // 步骤7: 展示预期响应
        console.log('📝 步骤7: 预期的 API 响应');
        console.log('🔍 成功的 API 响应格式:');
        console.log('  {');
        console.log('    "access_token": "real_access_token_from_augment"');
        console.log('  }');
        console.log('');

        // 步骤8: 总结
        console.log('🎉 测试说明完成！');
        console.log('='.repeat(50));
        console.log('');
        console.log('📋 关键点总结:');
        console.log('  ✅ 生成的授权 URL 是真实的 Augment URL');
        console.log('  ✅ OAuth 状态参数符合 PKCE 标准');
        console.log('  ✅ API 调用会向真实的 Augment 服务器发送请求');
        console.log('  ✅ 获取的访问令牌是真实可用的');
        console.log('');
        console.log('🚀 要执行真实的完整流程，请运行:');
        console.log('  node run-email-verification.js');
        console.log('');
        console.log('📊 流程说明:');
        console.log('  1. 🔐 自动生成真实的授权 URL');
        console.log('  2. 🤖 自动化完成邮箱验证');
        console.log('  3. 📋 获取真实的授权码');
        console.log('  4. 📡 调用真实的 Augment API');
        console.log('  5. 🔑 获取真实的访问令牌');
        console.log('  6. 💾 保存到 tokens.json 文件');
        console.log('');
        console.log('🔍 与演示脚本的区别:');
        console.log('  - demo-augment-auth.js: 使用 mock 数据，仅用于功能演示');
        console.log('  - run-email-verification.js: 使用真实数据，调用真实 API');
        console.log('  - test-real-api.js (本脚本): 说明真实 API 调用过程');

    } catch (error) {
        console.error('');
        console.error('💥 测试过程中出现错误:', error.message);
        console.error('📋 错误详情:', error.stack);
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    testRealAPI();
}

module.exports = testRealAPI;
