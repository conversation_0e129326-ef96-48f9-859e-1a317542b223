{"productName": "Augment Token Manager", "version": "0.1.0", "identifier": "com.capslockCube.augment-token-manager", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Augment Token Manager", "width": 800, "height": 700, "resizable": true, "fullscreen": false, "minWidth": 800, "minHeight": 700}], "security": {"csp": "default-src 'self'; connect-src ipc: http://ipc.localhost https://oauth.example.com"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico", "icons/icon.png"]}}